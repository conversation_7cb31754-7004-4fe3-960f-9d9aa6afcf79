# ModelSelect Component

## Overview

The `ModelSelect` component is a custom dropdown component designed to replace the basic HTML select element for AI model selection. It provides enhanced visual presentation by separating model names from their descriptions and displaying them with different styling.

## Features

- **Enhanced Display**: Shows model names prominently with descriptions beneath in smaller, lighter text
- **Custom Dropdown**: Fully customizable appearance with consistent styling
- **Keyboard Navigation**: Supports Enter, Escape, and Arrow keys
- **Accessibility**: Proper ARIA attributes and roles
- **Click Outside**: Automatically closes when clicking outside the component
- **Model Name Parsing**: Intelligently separates model names from descriptions using " - " delimiter

## Usage

```svelte
<script>
  import ModelSelect from '$lib/components/ModelSelect.svelte';
  import { availableModels } from '$lib/stores/unifiedSettingsStore';
  
  let selectedModel = '';
  
  function handleModelChange(event) {
    selectedModel = event.detail;
    console.log('Selected model:', selectedModel);
  }
</script>

<ModelSelect
  models={$availableModels}
  value={selectedModel}
  placeholder="Select a model"
  on:change={handleModelChange}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `models` | `ModelConfig[]` | `[]` | Array of available models |
| `value` | `string` | `''` | Currently selected model ID |
| `placeholder` | `string` | `'Select a model'` | Placeholder text when no model is selected |

## Events

| Event | Detail Type | Description |
|-------|-------------|-------------|
| `change` | `string` | Fired when a model is selected, detail contains the model ID |

## Model Data Structure

The component expects models to follow the `ModelConfig` interface:

```typescript
interface ModelConfig {
  id: string;        // Unique identifier (e.g., "gpt-4o")
  name: string;      // Display name with description (e.g., "GPT-4o - Flagship, Intelligent, Multimodal")
}
```

## Name Parsing Logic

The component automatically parses the `name` field to separate the model name from its description:

- **Input**: `"GPT-4o - Flagship, Intelligent, Multimodal"`
- **Parsed Name**: `"GPT-4o"`
- **Parsed Description**: `"Flagship, Intelligent, Multimodal"`

For models with multiple dashes:
- **Input**: `"Model - Part1 - Part2 - Part3"`
- **Parsed Name**: `"Model"`
- **Parsed Description**: `"Part1 - Part2 - Part3"`

## Styling

The component uses CSS custom properties for theming:

- `--color-border`: Border color
- `--color-input-bg`: Background color for the trigger
- `--color-text-primary`: Primary text color
- `--color-accent`: Accent color for focus states
- `--color-modal-bg`: Background color for dropdown
- `--color-shadow`: Shadow color
- `--color-toggle-hover`: Hover background color

## Keyboard Navigation

- **Enter/Space**: Open/close dropdown
- **Escape**: Close dropdown
- **Arrow Down**: Open dropdown when closed
- **Tab**: Navigate to next focusable element

## Accessibility

The component includes proper accessibility features:

- `role="button"` on the trigger
- `role="listbox"` on the dropdown
- `role="option"` on each item
- `aria-haspopup="listbox"` on the trigger
- `aria-expanded` to indicate dropdown state
- `aria-selected` on selected options
- `tabindex` for keyboard navigation

## Integration with Sidebar

The component is integrated into the `Sidebar.svelte` component, replacing the previous HTML select element:

```svelte
<!-- Before -->
<select bind:value={$appSettings.aiModel}>
  {#each $availableModels as model}
    <option value={model.id}>{model.name}</option>
  {/each}
</select>

<!-- After -->
<ModelSelect
  models={$availableModels}
  value={$appSettings.aiModel}
  on:change={(e) => updateAppSetting('aiModel', e.detail)}
/>
```

## Testing

The component includes comprehensive unit tests covering:

- Model name parsing logic
- Edge cases (empty names, multiple dashes, whitespace)
- Integration with the Sidebar component
- Event handling and state management

Tests are located in:
- `tests/ModelSelect.test.ts` - Core parsing logic tests
- `tests/SidebarModelSelect.test.ts` - Integration tests

## Browser Compatibility

The component uses modern JavaScript features and is compatible with:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Performance Considerations

- Lightweight implementation with minimal dependencies
- Efficient event handling with proper cleanup
- No external libraries required
- Optimized for fast rendering and interaction
